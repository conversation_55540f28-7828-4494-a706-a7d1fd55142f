import { useState, useEffect } from 'react'
import { FaSearch } from 'react-icons/fa'
import { motion } from 'framer-motion'
import { getAllCars } from '../../services/carService'
import PageHeader from '../../components/UI/PageHeader'
import CarCard from '../../components/Cars/CarCard'
import CarFilters from '../../components/Cars/CarFilters'
import Spinner from '../../components/UI/Spinner'

const UserDashboard = () => {
  const [cars, setCars] = useState([])
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState({})
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    const fetchCars = async () => {
      try {
        setLoading(true)
        const carsData = await getAllCars(filters)
        setCars(carsData)
      } catch (error) {
        console.error('Error fetching cars:', error)
        toast.error('Failed to load cars. Please try again later.')
        setCars([])
      } finally {
        setLoading(false)
      }
    }

    fetchCars()
  }, [filters])

  const handleFilterChange = (newFilters) => {
    setFilters({ ...newFilters })
  }

  const handleSearch = (e) => {
    e.preventDefault()
    setFilters({ ...filters, search: searchTerm })
  }

  // Filter cars by search term on the client side if API doesn't support it
  const filteredCars = searchTerm && !filters.search
    ? cars.filter(car =>
        car.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        car.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : cars

  return (
    <div>
      <PageHeader
        title="Browse Our Fleet"
        subtitle="Find and book your perfect ride from our premium collection"
        image="https://images.pexels.com/photos/3786091/pexels-photo-3786091.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
      />

      <div className="container mx-auto px-4 py-8">
        {/* Search */}
        <div className="mb-6">
          <form onSubmit={handleSearch}>
            <div className="relative">
              <input
                type="text"
                placeholder="Search for cars, models, brands..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input pl-10 pr-16 py-3"
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center">
                <FaSearch className="text-gray-400" />
              </div>
              <button
                type="submit"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 btn-primary py-1"
              >
                Search
              </button>
            </div>
          </form>
        </div>

        {/* Filters */}
        <CarFilters onFilterChange={handleFilterChange} initialFilters={filters} />

        {/* Results */}
        {loading ? (
          <div className="flex justify-center py-12">
            <Spinner size="large" />
          </div>
        ) : filteredCars.length === 0 ? (
          <div className="text-center py-12">
            <h3 className="text-2xl font-semibold mb-2">No cars found</h3>
            <p className="text-gray-600">Try adjusting your search filters</p>
          </div>
        ) : (
          <motion.div
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            {filteredCars.map(car => (
              <CarCard key={car.id} car={car} />
            ))}
          </motion.div>
        )}
      </div>
    </div>
  )
}

export default UserDashboard
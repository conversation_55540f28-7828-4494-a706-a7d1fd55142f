import axios from 'axios'
import { toast } from 'react-toastify'

// Use relative URL for the API base URL to leverage the Vite proxy
// This will be proxied to http://localhost:8080/api by Vite
const BASE_URL = '/api'

// For direct API calls (bypassing the proxy) - use this for debugging
// const DIRECT_BASE_URL = 'http://localhost:8080/api'

const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  }
})

// Request interceptor for adding the auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
      console.log('Adding auth token to request:', config.url)
    } else {
      console.log('No auth token available for request:', config.url)
    }
    return config
  },
  (error) => {
    console.error('Request interceptor error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor for handling errors
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.config.url, response.status, response.data)
    return response
  },
  (error) => {
    const { response, config } = error

    console.error('API Error:', config?.url, response?.status, response?.data || error.message)

    // For development - log detailed error information
    if (import.meta.env.DEV) {
      console.group('Detailed API Error Information')
      console.error('Request URL:', config?.url)
      console.error('Request Method:', config?.method)
      console.error('Request Headers:', config?.headers)
      console.error('Request Data:', config?.data)
      console.error('Response Status:', response?.status)
      console.error('Response Data:', response?.data)
      console.error('Error Message:', error.message)
      console.error('Error Stack:', error.stack)
      console.groupEnd()
    }

    if (response?.status === 401) {
      localStorage.removeItem('token')
      if (window.location.pathname !== '/login') {
        toast.error('Your session has expired. Please log in again.')
        window.location.href = '/login'
      }
    } else if (response?.status === 403) {
      toast.error('You do not have permission to perform this action')
    } else if (response?.status === 404) {
      // Handle 404 errors silently in most cases
      console.warn('Resource not found:', config?.url)
    } else if (response?.status === 500) {
      toast.error('Server error. Please try again later.')
      console.error('Server error details:', response?.data)
    } else if (!response) {
      // Network error - could be CORS, server down, etc.
      console.error('Network error details:', error)

      // Check if it's a CORS error
      if (error.message.includes('Network Error') || error.message.includes('CORS')) {
        toast.error('Network error: Unable to connect to the server. This may be due to CORS restrictions.')
        console.warn('This might be a CORS issue. Make sure the backend allows requests from this origin.')
      } else {
        toast.error('Network error. Please check if the backend server is running.')
      }
    }

    return Promise.reject(error)
  }
)

export default api
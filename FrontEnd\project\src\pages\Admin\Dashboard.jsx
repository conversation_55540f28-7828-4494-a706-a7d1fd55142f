import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { FaCar, FaClipboardList, FaUserFriends, FaDollarSign, FaChartLine, FaPlus } from 'react-icons/fa'
import { motion } from 'framer-motion'
import { toast } from 'react-toastify'
import { getAllBookings } from '../../services/bookingService'
import { getAllCars } from '../../services/carService'
import { getDashboardData } from '../../services/adminService'
import PageHeader from '../../components/UI/PageHeader'
import Spinner from '../../components/UI/Spinner'

const AdminDashboard = () => {
  const [bookings, setBookings] = useState([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    totalBookings: 0,
    activeBookings: 0,
    upcomingBookings: 0,
    completedBookings: 0,
    cancelledBookings: 0,
    totalRevenue: 0,
    totalCars: 0,
    availableCars: 0,
    bookedCars: 0,
    maintenanceCars: 0,
    revenueByMonth: [],
    popularCars: []
  })

  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const [dashboardError, setDashboardError] = useState(null)

  const refreshDashboard = () => {
    setRefreshTrigger(prev => prev + 1)
  }

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true)
        setDashboardError(null)

        console.log('Fetching dashboard data from server...')

        // Fetch dashboard data and bookings in parallel
        const [dashboardData, bookingsData] = await Promise.all([
          getDashboardData(),
          getAllBookings()
        ])

        console.log('Dashboard data received:', dashboardData)
        console.log('Bookings data received:', bookingsData)

        // Process bookings data
        if (Array.isArray(bookingsData)) {
          setBookings(bookingsData)
        } else {
          console.error('Unexpected bookings data format:', bookingsData)
          setBookings([])
        }

        // Process dashboard data
        if (dashboardData) {
          setStats({
            totalBookings: dashboardData.totalBookings ?? bookingsData.length,
            activeBookings: dashboardData.activeBookings ?? bookingsData.filter(booking => booking.status === 'ACTIVE').length,
            upcomingBookings: dashboardData.upcomingBookings ?? bookingsData.filter(booking => booking.status === 'CONFIRMED').length,
            completedBookings: dashboardData.completedBookings ?? bookingsData.filter(booking => booking.status === 'COMPLETED').length,
            cancelledBookings: dashboardData.cancelledBookings ?? bookingsData.filter(booking => booking.status === 'CANCELLED').length,
            totalRevenue: dashboardData.totalRevenue ?? bookingsData.reduce((sum, booking) => sum + (booking.totalPrice || 0), 0),
            totalCars: dashboardData.totalCars ?? 0,
            availableCars: dashboardData.availableCars ?? 0,
            bookedCars: dashboardData.bookedCars ?? 0,
            maintenanceCars: dashboardData.maintenanceCars ?? 0,
            revenueByMonth: dashboardData.revenueByMonth ?? [],
            popularCars: dashboardData.popularCars ?? []
          })
        } else {
          throw new Error('No dashboard data received from server')
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error)
        setDashboardError(error.message || 'Failed to load dashboard data')
        toast.error('Failed to load dashboard data. Please try again later.')
        setBookings([])
        setStats({
          totalBookings: 0,
          activeBookings: 0,
          upcomingBookings: 0,
          completedBookings: 0,
          cancelledBookings: 0,
          totalRevenue: 0,
          totalCars: 0,
          availableCars: 0,
          bookedCars: 0,
          maintenanceCars: 0,
          revenueByMonth: [],
          popularCars: []
        })
      } finally {
        setLoading(false)
      }
    }

    fetchDashboardData()
  }, [refreshTrigger])

  return (
    <div>
      <PageHeader
        title="Admin Dashboard"
        subtitle="Manage bookings, cars, and view analytics"
      />

      <div className="container mx-auto px-4 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            className="bg-white rounded-xl shadow-card p-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-primary-100 p-3 rounded-full">
                <FaClipboardList className="text-xl text-primary-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-gray-500 text-sm">Total Bookings</h3>
                <p className="text-2xl font-semibold">{stats.totalBookings}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {stats.completedBookings} completed
                </p>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="bg-white rounded-xl shadow-card p-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-green-100 p-3 rounded-full">
                <FaUserFriends className="text-xl text-green-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-gray-500 text-sm">Active Bookings</h3>
                <p className="text-2xl font-semibold">{stats.activeBookings}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {stats.upcomingBookings} upcoming
                </p>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="bg-white rounded-xl shadow-card p-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-accent-100 p-3 rounded-full">
                <FaDollarSign className="text-xl text-accent-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-gray-500 text-sm">Total Revenue</h3>
                <p className="text-2xl font-semibold">${stats.totalRevenue.toLocaleString()}</p>
                <p className="text-xs text-gray-500 mt-1">
                  From {stats.totalBookings} bookings
                </p>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="bg-white rounded-xl shadow-card p-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
          >
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-blue-100 p-3 rounded-full">
                <FaCar className="text-xl text-blue-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-gray-500 text-sm">Total Cars</h3>
                <p className="text-2xl font-semibold">{stats.totalCars}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {stats.availableCars} available, {stats.bookedCars} booked
                </p>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Quick Actions */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6">
          <div className="flex items-center">
            <h2 className="text-2xl font-semibold mb-4 sm:mb-0 mr-4">Recent Bookings</h2>
            <button
              onClick={refreshDashboard}
              className="btn-outline-sm flex items-center mb-4 sm:mb-0"
              disabled={loading}
            >
              {loading ? (
                <span className="animate-spin mr-1">⟳</span>
              ) : (
                <span className="mr-1">⟳</span>
              )}
              Refresh
            </button>
          </div>
          <div className="flex space-x-3">
            <Link to="/admin/cars" className="btn-outline flex items-center">
              <FaCar className="mr-2" />
              Manage Cars
            </Link>
            <Link to="/admin/cars/new" className="btn-primary flex items-center">
              <FaPlus className="mr-2" />
              Add New Car
            </Link>
          </div>
        </div>

        {/* Error Display */}
        {dashboardError && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6 flex items-start">
            <div className="text-red-500 mr-3 mt-1">⚠️</div>
            <div>
              <p className="font-medium">Error loading dashboard data</p>
              <p className="text-sm">{dashboardError}</p>
              <p className="text-sm mt-1">
                The dashboard may display incomplete or outdated information.
                <button
                  onClick={refreshDashboard}
                  className="text-primary-600 underline ml-2"
                >
                  Try again
                </button>
              </p>
            </div>
          </div>
        )}

        {/* Bookings Table */}
        {loading ? (
          <div className="flex justify-center py-12">
            <Spinner size="large" />
          </div>
        ) : (
          <div className="bg-white rounded-xl shadow-card overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Booking ID
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Customer
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Car
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Dates
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {bookings.map((booking) => (
                    <tr key={booking.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-primary-600">
                          {booking.bookingReference || `#${booking.id}`}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{booking.customerName || booking.name || 'N/A'}</div>
                        <div className="text-sm text-gray-500">{booking.customerEmail || booking.email || 'N/A'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{booking.carName || booking.car?.name || 'N/A'}</div>
                        <div className="text-sm text-gray-500">
                          {booking.carBrand || booking.car?.brand || ''} {booking.carModel || booking.car?.model || ''}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {booking.startDate ? new Date(booking.startDate).toLocaleDateString() : 'N/A'} -
                          {booking.endDate ? new Date(booking.endDate).toLocaleDateString() : 'N/A'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {booking.duration ||
                            (booking.startDate && booking.endDate ?
                              Math.ceil((new Date(booking.endDate) - new Date(booking.startDate)) / (1000 * 60 * 60 * 24)) :
                              0)} days
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          booking.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                          booking.status === 'ACTIVE' ? 'bg-blue-100 text-blue-800' :
                          booking.status === 'CONFIRMED' ? 'bg-indigo-100 text-indigo-800' :
                          booking.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {booking.status}
                        </span>
                        {booking.paymentStatus && (
                          <div className="mt-1">
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              booking.paymentStatus === 'PAID' ? 'bg-green-50 text-green-600' : 'bg-yellow-50 text-yellow-600'
                            }`}>
                              {booking.paymentStatus}
                            </span>
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">${(booking.totalPrice || 0).toLocaleString()}</div>
                        <div className="text-xs text-gray-500">
                          {booking.paymentMethod || 'Card payment'}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {bookings.length === 0 && (
              <div className="text-center py-12">
                <p className="text-gray-500">No bookings found</p>
              </div>
            )}
          </div>
        )}

        {/* Revenue Overview */}
        <div className="mt-8 bg-white rounded-xl shadow-card p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold">Revenue Overview</h2>
            <div className="flex items-center">
              <select className="input py-1 px-3 text-sm">
                <option>Last 7 Days</option>
                <option>Last Month</option>
                <option>Last Quarter</option>
                <option>This Year</option>
              </select>
            </div>
          </div>

          {/* Revenue Chart */}
          <div className="mb-8">
            <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
              {loading ? (
                <div className="text-center">
                  <Spinner size="medium" />
                  <p className="text-gray-500 mt-2">Loading revenue data...</p>
                </div>
              ) : stats.revenueByMonth && stats.revenueByMonth.length > 0 ? (
                <div className="w-full h-full p-4">
                  <div className="flex justify-between mb-2">
                    <div className="text-sm font-medium text-gray-600">Monthly Revenue</div>
                    <div className="text-sm font-medium text-primary-600">${stats.totalRevenue.toLocaleString()}</div>
                  </div>
                  <div className="relative h-48">
                    <div className="absolute bottom-0 left-0 right-0 flex items-end justify-between h-40">
                      {stats.revenueByMonth.map((item, index) => {
                        const maxRevenue = Math.max(...stats.revenueByMonth.map(i => i.revenue || 0))
                        const height = maxRevenue > 0
                          ? `${((item.revenue || 0) / maxRevenue) * 100}%`
                          : '4px'

                        return (
                          <div key={index} className="flex flex-col items-center w-1/6">
                            <div
                              className="w-12 bg-primary-500 rounded-t-sm relative group"
                              style={{
                                height: height,
                                minHeight: '4px'
                              }}
                            >
                              {/* Tooltip */}
                              <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                                ${(item.revenue || 0).toLocaleString()}
                              </div>
                            </div>
                            <div className="text-xs text-gray-500 mt-1">{item.month}</div>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center">
                  <FaChartLine className="text-4xl text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-500">No revenue data available</p>
                  {dashboardError && (
                    <button
                      onClick={refreshDashboard}
                      className="text-primary-600 underline mt-2 text-sm"
                    >
                      Refresh data
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Popular Cars */}
          <div>
            <h3 className="text-lg font-medium mb-4">Popular Cars</h3>
            {loading ? (
              <div className="text-center py-8">
                <Spinner size="medium" />
                <p className="text-gray-500 mt-2">Loading popular cars data...</p>
              </div>
            ) : stats.popularCars && stats.popularCars.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Car</th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bookings</th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Availability</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {stats.popularCars.map((car, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{car.name}</div>
                          {car.id && (
                            <Link to={`/admin/cars/${car.id}`} className="text-xs text-primary-600 hover:underline">
                              View details
                            </Link>
                          )}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{car.bookings}</div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">${(car.revenue || 0).toLocaleString()}</div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            car.available ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {car.available ? 'Available' : 'Booked'}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 bg-gray-50 rounded-lg">
                <p className="text-gray-500">No popular cars data available</p>
                {dashboardError && (
                  <button
                    onClick={refreshDashboard}
                    className="text-primary-600 underline mt-2 text-sm"
                  >
                    Refresh data
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default AdminDashboard
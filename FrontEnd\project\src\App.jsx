import { useEffect, lazy, Suspense } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuth } from './contexts/AuthContext'
import Layout from './components/Layout/Layout'
import Spinner from './components/UI/Spinner'
import ProtectedRoute from './components/Routes/ProtectedRoute'
import AdminRoute from './components/Routes/AdminRoute'

// Lazy-loaded components for better performance
const Home = lazy(() => import('./pages/Home'))
const Login = lazy(() => import('./pages/Auth/Login'))
const Register = lazy(() => import('./pages/Auth/Register'))
const UserDashboard = lazy(() => import('./pages/User/Dashboard'))
const CarDetails = lazy(() => import('./pages/User/CarDetails'))
const Booking = lazy(() => import('./pages/User/Booking'))
const Payment = lazy(() => import('./pages/User/Payment'))
const BookingSuccess = lazy(() => import('./pages/User/BookingSuccess'))
const MyBookings = lazy(() => import('./pages/User/MyBookings'))
const AdminDashboard = lazy(() => import('./pages/Admin/Dashboard'))
const AdminCars = lazy(() => import('./pages/Admin/Cars'))
const AdminNewCar = lazy(() => import('./pages/Admin/NewCar'))
const ApiTestPage = lazy(() => import('./pages/Admin/ApiTestPage'))
const NotFound = lazy(() => import('./pages/NotFound'))

function App() {
  const { checkAuthStatus } = useAuth()

  useEffect(() => {
    checkAuthStatus()
  }, [checkAuthStatus])

  return (
    <Suspense fallback={<div className="flex justify-center items-center h-screen"><Spinner size="large" /></div>}>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<Home />} />
          <Route path="login" element={<Login />} />
          <Route path="register" element={<Register />} />
          <Route path="api-test" element={<ApiTestPage />} />

          {/* User Routes */}
          <Route path="user" element={<ProtectedRoute />}>
            <Route path="dashboard" element={<UserDashboard />} />
            <Route path="cars/:id" element={<CarDetails />} />
            <Route path="booking/:id" element={<Booking />} />
            <Route path="payment/:bookingId" element={<Payment />} />
            <Route path="booking-success/:bookingId" element={<BookingSuccess />} />
            <Route path="my-bookings" element={<MyBookings />} />
          </Route>

          {/* Admin Routes */}
          <Route path="admin" element={<AdminRoute />}>
            <Route path="dashboard" element={<AdminDashboard />} />
            <Route path="cars" element={<AdminCars />} />
            <Route path="cars/new" element={<AdminNewCar />} />
            <Route path="cars/edit/:id" element={<AdminNewCar />} />
            <Route path="api-test" element={<ApiTestPage />} />
          </Route>

          <Route path="404" element={<NotFound />} />
          <Route path="*" element={<Navigate to="/404" replace />} />
        </Route>
      </Routes>
    </Suspense>
  )
}

export default App